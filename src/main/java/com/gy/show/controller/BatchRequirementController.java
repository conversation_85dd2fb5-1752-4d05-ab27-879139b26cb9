package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.service.BatchRequirementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 批量需求控制器
 */
@Slf4j
@Api(tags = "批量需求管理")
@RestController
@RequestMapping("/api/batch-requirement")
public class BatchRequirementController {
    
    @Autowired
    private BatchRequirementService batchRequirementService;
    
    @ApiOperation("批量创建需求")
    @PostMapping("/create")
    public Result<Integer> batchCreateRequirements(@Valid @RequestBody BatchRequirementCreateDTO batchConfig) {
        try {
            log.info("接收到批量创建需求请求，目标数量：{}", batchConfig.getTargetCount());
            
            Integer successCount = batchRequirementService.batchCreateRequirements(batchConfig);
            
            log.info("批量创建需求完成，成功创建{}个需求", successCount);
            return Result.success(successCount, "批量创建需求成功");
            
        } catch (Exception e) {
            log.error("批量创建需求失败", e);
            return Result.error("批量创建需求失败：" + e.getMessage());
        }
    }
}
