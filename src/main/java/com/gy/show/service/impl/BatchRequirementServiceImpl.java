package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.BatchRequirementService;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.DataPresetTrackInfoService;
import com.gy.show.service.RequirementInfoService;
import com.gy.show.util.BatchRequirementCreator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 批量需求服务实现类
 */
@Slf4j
@Service
public class BatchRequirementServiceImpl implements BatchRequirementService {
    
    @Autowired
    private RequirementInfoService requirementInfoService;
    
    @Autowired
    private DataGeneralService dataGeneralService;
    
    @Autowired
    private DataPresetTrackInfoService dataPresetTrackInfoService;
    
    @Autowired
    private CommonMapper commonMapper;
    
    @Autowired
    private BatchRequirementCreator batchRequirementCreator;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchCreateRequirements(BatchRequirementCreateDTO batchConfig) {
        log.info("开始批量创建需求，目标数量：{}", batchConfig.getTargetCount());
        
        try {
            // 1. 获取数据库中的目标和航迹数据
            List<String> targetIds = getTargetIds(batchConfig.getTargetCount());
            List<String> trackIds = getTrackIds(batchConfig.getTargetCount());
            List<String> generalIds = getGeneralIds(batchConfig.getTargetCount());
            
            // 2. 生成需求列表
            List<RequirementInfoDTO> requirements = batchRequirementCreator.createBatchRequirements(
                batchConfig, targetIds, trackIds, generalIds);
            
            // 3. 批量创建需求
            int successCount = 0;
            for (RequirementInfoDTO requirement : requirements) {
                try {
                    requirementInfoService.addRequirementInfo(requirement);
                    successCount++;
                    
                    if (successCount % 100 == 0) {
                        log.info("已成功创建{}个需求", successCount);
                    }
                } catch (Exception e) {
                    log.error("创建需求失败：{}", requirement.getRequirementName(), e);
                    // 继续处理下一个需求，不中断整个批量操作
                }
            }
            
            log.info("批量创建需求完成，成功创建{}个需求", successCount);
            return successCount;
            
        } catch (Exception e) {
            log.error("批量创建需求失败", e);
            throw new ServiceException("批量创建需求失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取目标ID列表
     */
    private List<String> getTargetIds(Integer count) {
        List<String> targetIds = new ArrayList<>();

        // 查询所有目标类型的数据表
        List<DataGeneral> generals = dataGeneralService.list(
            Wrappers.<DataGeneral>lambdaQuery()
                .in(DataGeneral::getDataType, 8, 9, 10) // 无人机、无人车、无人艇
        );

        if (generals.isEmpty()) {
            throw new ServiceException("未找到目标数据表");
        }

        int currentCount = 0;
        for (DataGeneral general : generals) {
            if (currentCount >= count) {
                break;
            }

            // 从每个表中查询目标数据
            int pageSize = Math.min(count - currentCount, 500); // 每次最多查询500条
            IPage<Map<String, Object>> page = new Page<>(1, pageSize);
            IPage<Map<String, Object>> result = commonMapper.getData(page, general.getTableName(), null, null, null);
            List<Map<String, Object>> targets = result.getRecords();

            for (Map<String, Object> target : targets) {
                if (currentCount >= count) {
                    break;
                }
                targetIds.add(target.get("id").toString());
                currentCount++;
            }
        }

        if (targetIds.size() < count) {
            throw new ServiceException("数据库中的目标数量不足，需要" + count + "个，实际只有" + targetIds.size() + "个");
        }

        return targetIds.subList(0, count);
    }
    
    /**
     * 获取航迹ID列表
     */
    private List<String> getTrackIds(Integer count) {
        List<DataPresetTrackInfo> tracks = dataPresetTrackInfoService.list(
            Wrappers.<DataPresetTrackInfo>lambdaQuery()
                .last("LIMIT " + count)
        );
        
        if (tracks.size() < count) {
            throw new ServiceException("数据库中的航迹数量不足，需要" + count + "个，实际只有" + tracks.size() + "个");
        }
        
        List<String> trackIds = new ArrayList<>();
        for (DataPresetTrackInfo track : tracks) {
            trackIds.add(track.getId());
        }
        
        return trackIds;
    }
    
    /**
     * 获取主表ID列表
     */
    private List<String> getGeneralIds(Integer count) {
        List<String> generalIds = new ArrayList<>();
        
        // 查询所有目标类型的数据表
        List<DataGeneral> generals = dataGeneralService.list(
            Wrappers.<DataGeneral>lambdaQuery()
                .in(DataGeneral::getDataType, 8, 9, 10) // 无人机、无人车、无人艇
        );
        
        if (generals.isEmpty()) {
            throw new ServiceException("未找到目标数据表");
        }
        
        // 按比例分配每种类型的目标数量
        int perTypeCount = count / generals.size();
        int remainder = count % generals.size();
        
        for (int i = 0; i < generals.size(); i++) {
            DataGeneral general = generals.get(i);
            int currentTypeCount = perTypeCount + (i < remainder ? 1 : 0);
            
            for (int j = 0; j < currentTypeCount; j++) {
                generalIds.add(general.getId());
            }
        }
        
        return generalIds;
    }
}
