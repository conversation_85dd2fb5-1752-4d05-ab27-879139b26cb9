package com.gy.show.util;

import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.enums.RequirementSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 批量需求创建工具类
 */
@Slf4j
@Component
public class BatchRequirementCreator {
    
    /**
     * 根据批量创建配置生成需求列表
     * 
     * @param batchConfig 批量创建配置
     * @param targetIds 目标ID列表
     * @param trackIds 航迹ID列表
     * @param generalIds 主表ID列表
     * @return 需求DTO列表
     */
    public List<RequirementInfoDTO> createBatchRequirements(
            BatchRequirementCreateDTO batchConfig,
            List<String> targetIds,
            List<String> trackIds,
            List<String> generalIds) {
        
        List<RequirementInfoDTO> requirements = new ArrayList<>();
        
        // 验证数据
        if (targetIds.size() != batchConfig.getTargetCount() ||
            trackIds.size() != batchConfig.getTargetCount() ||
            generalIds.size() != batchConfig.getTargetCount()) {
            throw new IllegalArgumentException("目标数量、航迹数量和主表ID数量必须一致");
        }
        
        log.info("开始批量创建{}个需求", batchConfig.getTargetCount());
        
        for (int i = 0; i < batchConfig.getTargetCount(); i++) {
            RequirementInfoDTO requirement = createSingleRequirement(
                batchConfig, 
                targetIds.get(i), 
                trackIds.get(i), 
                generalIds.get(i), 
                i + 1
            );
            requirements.add(requirement);
        }
        
        log.info("批量需求创建完成，共创建{}个需求", requirements.size());
        return requirements;
    }
    
    /**
     * 创建单个需求
     */
    private RequirementInfoDTO createSingleRequirement(
            BatchRequirementCreateDTO batchConfig,
            String targetId,
            String trackId,
            String generalId,
            int index) {
        
        RequirementInfoDTO requirement = new RequirementInfoDTO();
        
        // 设置需求基本信息
        requirement.setRequirementName(batchConfig.getRequirementNamePrefix() + "-" + String.format("%04d", index));
        requirement.setRequirementType(batchConfig.getRequirementType());
        requirement.setImportance(batchConfig.getImportance());
        requirement.setStartTime(batchConfig.getStartTime());
        requirement.setEndTime(batchConfig.getEndTime());
        requirement.setRequirementComment(batchConfig.getRequirementComment());
        requirement.setSource(RequirementSourceEnum.MANUAL.getCode());
        requirement.setIsDeCompose(1); // 自动分解
        
        // 创建目标信息
        RequirementInfoDTO.TargetInfo targetInfo = new RequirementInfoDTO.TargetInfo();
        targetInfo.setTargetId(targetId);
        targetInfo.setGeneralId(generalId);
        targetInfo.setTrackPresetId(trackId);
        
        // 设置航迹时间
        LocalDateTime trackStartTime = batchConfig.getStartTime();
        if (batchConfig.getTrackConfig() != null && batchConfig.getTrackConfig().getTrackStartOffset() != null) {
            trackStartTime = trackStartTime.plusMinutes(batchConfig.getTrackConfig().getTrackStartOffset());
        }
        targetInfo.setTrackStartTime(trackStartTime);
        
        // 创建任务列表
        List<RequirementTaskDTO> tasks = createTasks(batchConfig, index);
        targetInfo.setTasks(tasks);
        
        // 设置目标信息列表
        requirement.setTargetInfos(Arrays.asList(targetInfo));
        
        return requirement;
    }
    
    /**
     * 创建任务列表
     */
    private List<RequirementTaskDTO> createTasks(BatchRequirementCreateDTO batchConfig, int index) {
        List<RequirementTaskDTO> tasks = new ArrayList<>();
        
        if (batchConfig.getTaskConfig() == null || batchConfig.getTaskConfig().getTaskTypes() == null) {
            // 默认创建测控任务
            RequirementTaskDTO task = createDefaultTask(batchConfig, index);
            tasks.add(task);
        } else {
            // 根据配置创建任务
            RequirementTaskDTO task = new RequirementTaskDTO();
            task.setTaskName("批量任务-" + String.format("%04d", index));
            task.setTaskType(batchConfig.getTaskConfig().getTaskTypes());
            task.setRepeatType(batchConfig.getTaskConfig().getRepeatType());
            
            // 设置任务时间
            LocalDateTime taskStartTime = batchConfig.getStartTime();
            if (batchConfig.getTaskConfig().getTaskStartOffset() != null) {
                taskStartTime = taskStartTime.plusMinutes(batchConfig.getTaskConfig().getTaskStartOffset());
            }
            task.setStartTime(taskStartTime);
            
            LocalDateTime taskEndTime = taskStartTime;
            if (batchConfig.getTaskConfig().getTaskDuration() != null) {
                taskEndTime = taskStartTime.plusMinutes(batchConfig.getTaskConfig().getTaskDuration());
            } else {
                taskEndTime = taskStartTime.plusHours(1); // 默认1小时
            }
            task.setEndTime(taskEndTime);
            
            tasks.add(task);
        }
        
        return tasks;
    }
    
    /**
     * 创建默认任务（测控任务）
     */
    private RequirementTaskDTO createDefaultTask(BatchRequirementCreateDTO batchConfig, int index) {
        RequirementTaskDTO task = new RequirementTaskDTO();
        task.setTaskName("批量测控任务-" + String.format("%04d", index));
        task.setTaskType(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
        task.setRepeatType(0); // 仅一次
        task.setStartTime(batchConfig.getStartTime());
        task.setEndTime(batchConfig.getStartTime().plusHours(1)); // 默认1小时
        
        return task;
    }
}
