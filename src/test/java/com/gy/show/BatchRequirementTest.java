package com.gy.show;

import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.service.BatchRequirementService;
import com.gy.show.util.BatchRequirementExample;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 批量需求创建测试类
 */
@Slf4j
@SpringBootTest
public class BatchRequirementTest {
    
    @Autowired
    private BatchRequirementService batchRequirementService;
    
    @Autowired
    private BatchRequirementExample batchRequirementExample;
    
    /**
     * 测试批量创建1000个目标的需求
     */
    @Test
    public void testCreate1000Targets() {
        try {
            log.info("开始测试批量创建1000个目标的需求");
            
            // 创建批量需求配置
            BatchRequirementCreateDTO batchConfig = batchRequirementExample.create1000TargetsExample();
            
            // 执行批量创建
            Integer successCount = batchRequirementService.batchCreateRequirements(batchConfig);
            
            log.info("批量创建完成，成功创建{}个需求", successCount);
            
            // 验证结果
            assert successCount > 0 : "应该至少创建一个需求";
            assert successCount <= 1000 : "创建的需求数量不应超过1000个";
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试批量创建测控任务需求
     */
    @Test
    public void testCreateControlTasks() {
        try {
            log.info("开始测试批量创建测控任务需求");
            
            // 创建批量需求配置
            BatchRequirementCreateDTO batchConfig = batchRequirementExample.createControlTaskExample();
            
            // 执行批量创建
            Integer successCount = batchRequirementService.batchCreateRequirements(batchConfig);
            
            log.info("批量创建测控任务完成，成功创建{}个需求", successCount);
            
            // 验证结果
            assert successCount > 0 : "应该至少创建一个需求";
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
    
    /**
     * 测试批量创建数传任务需求
     */
    @Test
    public void testCreateDataTransmissionTasks() {
        try {
            log.info("开始测试批量创建数传任务需求");
            
            // 创建批量需求配置
            BatchRequirementCreateDTO batchConfig = batchRequirementExample.createDataTransmissionExample();
            
            // 执行批量创建
            Integer successCount = batchRequirementService.batchCreateRequirements(batchConfig);
            
            log.info("批量创建数传任务完成，成功创建{}个需求", successCount);
            
            // 验证结果
            assert successCount > 0 : "应该至少创建一个需求";
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }
}
