# 批量需求创建功能说明

## 概述

本功能用于批量创建大量目标的需求，解决手动创建1000个目标需求耗时过长的问题。系统会自动从数据库中获取已有的目标和航迹数据，并根据配置批量生成需求。

## 功能特点

1. **批量创建**：支持一次性创建大量需求（如1000个）
2. **自动分配**：自动从数据库中获取目标和航迹数据
3. **任务合并**：根据任务类型自动合并（测控类型任务会合并，数传任务独立处理）
4. **配置灵活**：支持多种任务类型和时间配置
5. **事务安全**：使用事务确保数据一致性

## 核心类说明

### 1. BatchRequirementCreateDTO
批量创建需求的配置DTO，包含：
- 需求基本信息（名称、类型、重要程度、时间等）
- 任务配置（任务类型、持续时间、重复类型等）
- 航迹配置（时间偏移、是否使用现有航迹等）

### 2. BatchRequirementCreator
批量需求创建工具类，负责：
- 根据配置生成需求列表
- 创建目标信息和任务信息
- 处理时间计算和数据转换

### 3. BatchRequirementService
批量需求服务接口和实现，负责：
- 从数据库获取目标和航迹数据
- 调用创建工具生成需求
- 批量调用addRequirementInfo方法创建需求

## 使用方法

### 1. API调用方式

```http
POST /api/batch-requirement/create
Content-Type: application/json

{
  "requirementNamePrefix": "批量需求测试",
  "requirementType": 1,
  "importance": 1,
  "startTime": "2024-08-05T10:00:00",
  "endTime": "2024-08-06T10:00:00",
  "requirementComment": "批量创建1000个目标的需求测试",
  "targetCount": 1000,
  "taskConfig": {
    "taskTypes": [1, 2, 3],
    "taskStartOffset": 30,
    "taskDuration": 120,
    "repeatType": 0
  },
  "trackConfig": {
    "trackStartOffset": 0,
    "useExistingTrack": true
  }
}
```

### 2. 代码调用方式

```java
@Autowired
private BatchRequirementService batchRequirementService;

@Autowired
private BatchRequirementExample batchRequirementExample;

// 创建1000个目标的需求
BatchRequirementCreateDTO batchConfig = batchRequirementExample.create1000TargetsExample();
Integer successCount = batchRequirementService.batchCreateRequirements(batchConfig);
```

### 3. 测试方式

运行测试类：
```bash
mvn test -Dtest=BatchRequirementTest#testCreate1000Targets
```

## 配置参数说明

### 需求基本信息
- `requirementNamePrefix`: 需求名称前缀，系统会自动添加序号
- `requirementType`: 需求类型（1=预规划任务，2=随遇接入任务）
- `importance`: 重要程度（1=一般，2=重要）
- `startTime`: 需求开始时间
- `endTime`: 需求结束时间
- `requirementComment`: 需求描述
- `targetCount`: 要创建的目标数量

### 任务配置
- `taskTypes`: 任务类型列表（1=遥控，2=遥测，3=测量，4=数传）
- `taskStartOffset`: 任务开始时间偏移（相对于需求开始时间，单位：分钟）
- `taskDuration`: 任务持续时间（单位：分钟）
- `repeatType`: 重复类型（0=仅一次，1=每日重复，2=每月重复）

### 航迹配置
- `trackStartOffset`: 航迹开始时间偏移（相对于需求开始时间，单位：分钟）
- `useExistingTrack`: 是否使用现有航迹（true=使用数据库中的航迹）

## 任务类型处理逻辑

根据现有的需求分解逻辑，系统会对任务类型进行特殊处理：

1. **测控类型任务**（遥控、遥测、测量）：
   - 时间有交集的任务会被合并为一个测控任务（任务类型=5）
   - 没有交集的任务保持独立

2. **数传任务**：
   - 保持独立，不与其他任务合并
   - 每个数传任务单独创建

## 数据库要求

使用此功能前，请确保数据库中有足够的数据：

1. **目标数据**：
   - 需要在目标表中有足够数量的目标记录
   - 支持的目标类型：无人机(8)、无人车(9)、无人艇(10)

2. **航迹数据**：
   - 需要在预设航迹表中有足够数量的航迹记录
   - 每个目标需要对应一个航迹

## 注意事项

1. **性能考虑**：创建1000个需求可能需要较长时间，建议在低峰期执行
2. **数据验证**：系统会验证数据库中的目标和航迹数量是否足够
3. **错误处理**：如果某个需求创建失败，不会影响其他需求的创建
4. **事务管理**：整个批量创建过程在事务中执行，确保数据一致性

## 示例场景

### 场景1：创建1000个测控任务
```java
BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
config.setRequirementNamePrefix("测控任务批量");
config.setTargetCount(1000);
config.getTaskConfig().setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
```

### 场景2：创建1000个数传任务
```java
BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
config.setRequirementNamePrefix("数传任务批量");
config.setTargetCount(1000);
config.getTaskConfig().setTaskTypes(Arrays.asList(4)); // 数传
```

### 场景3：创建混合任务
```java
BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
config.setRequirementNamePrefix("混合任务批量");
config.setTargetCount(1000);
config.getTaskConfig().setTaskTypes(Arrays.asList(1, 2, 3, 4)); // 所有类型
```

## 监控和日志

系统会记录详细的执行日志：
- 批量创建开始和结束时间
- 每100个需求创建完成的进度提示
- 创建失败的需求及错误信息
- 最终成功创建的需求数量

通过日志可以监控批量创建的进度和结果。
